import { BaseComponent } from './base';
import { LocationUtil } from './utils/location.util';
import { SecondaryFilterComponent } from './secondary-filter';

export class PromotionFilterPrimary extends BaseComponent {
  constructor() {
    super();
    this.CLASS_FILTER_SELECTED = 'filter-selected';
    this.secondaryFilterInstance = null;
    this.init();
  }

  typesTilter = {
    cardTypes: 'card-types',
    products: 'products',
    types: 'types',
  };

  urlParams = new URLSearchParams(location.search);
  paramCardTypes = LocationUtil.getUrlParamObj()[this.typesTilter.cardTypes] || '';
  paramProducts = LocationUtil.getUrlParamObj()[this.typesTilter.products] || '';
  paramTypes = LocationUtil.getUrlParamObj()[this.typesTilter.types] || '';

  // Get or create SecondaryFilterComponent instance (singleton pattern)
  getSecondaryFilterInstance() {
    if (!this.secondaryFilterInstance) {
      // Use static getInstance method to ensure singleton behavior
      this.secondaryFilterInstance = SecondaryFilterComponent.getInstance();

      // If getInstance returns null (no DOM element), try creating one
      if (!this.secondaryFilterInstance) {
        const existingElement = $('.promotion-hub-secondary');
        if (existingElement.length) {
          this.secondaryFilterInstance = new SecondaryFilterComponent();
        }
      }
    }
    return this.secondaryFilterInstance;
  }

  // Refresh the secondary filter without creating new instance
  refreshSecondaryFilter() {
    const instance = this.getSecondaryFilterInstance();
    if (instance && typeof instance.refreshFromURL === 'function') {
      // Use the new refresh method that updates URL and triggers promotions
      instance.refreshFromURL();
    } else if (instance && typeof instance.getPromotions === 'function') {
      // Fallback to manual URL update and promotions trigger
      instance.url = new URL(window.location);
      instance.getPromotions();
    }
  }

  initRenderUIFilter() {
    const promotionFilterPrimary = $('.promotionfilterprimary');
    const listFilterItem = $('.credit-card-listing__button');
    if (!promotionFilterPrimary.length || !listFilterItem.length) {
      return;
    }

    listFilterItem.each((_, el) => {
      const $item = $(el);
      const dataValueRaw = ($item.data('value') || '').toLowerCase();
      const dataParams = ($item.data('param') || '').toLowerCase();
      const dataValues = dataValueRaw.split(',');

      $item.on('click', (e) => {
        e.preventDefault();
        this.handleEventClickFilter($item);
      });

      let paramList = [];
      if (dataParams === this.typesTilter.cardTypes) {
        paramList = this.convertStringParamsToArray(this.paramCardTypes);
      } else if (dataParams === this.typesTilter.products) {
        paramList = this.convertStringParamsToArray(this.paramProducts);
      } else if (dataParams === this.typesTilter.types) {
        paramList = this.convertStringParamsToArray(this.paramTypes);
      }

      const hasAllSelected = dataValues.every((val) => paramList.includes(val));

      if (hasAllSelected) {
        $item.addClass(this.CLASS_FILTER_SELECTED);
      }
    });
  }

  convertStringParamsToArray(param) {
    if (!param) {
      return [];
    }
    return param.split(',');
  }

  handleUpdateParamsUrl(param, typeParam, isRemove) {
    if (!param) {
      return;
    }

    const urlParams = new URLSearchParams(window.location.search);
    const oldValue = urlParams.get(typeParam) || '';
    let list = [];
    if (oldValue) {
      list = oldValue.split(',');
    }

    if (isRemove) {
      list = list.filter((item) => item !== param);
    } else if (!list.includes(param)) {
      list.push(param);
    }

    if (list.length) {
      urlParams.set(typeParam, list.join(','));
    } else {
      urlParams.delete(typeParam);
    }

    let newUrl = window.location.pathname;
    if (urlParams.toString()) {
      newUrl += `?${urlParams.toString()}`;
    }

    window.history.pushState({}, '', newUrl);

    this.paramCardTypes = LocationUtil.getUrlParamObj()[this.typesTilter.cardTypes] || '';
    this.paramProducts = LocationUtil.getUrlParamObj()[this.typesTilter.products] || '';
    this.paramTypes = LocationUtil.getUrlParamObj()[this.typesTilter.types] || '';

    // Use singleton instance instead of creating new one
    this.refreshSecondaryFilter();
  }

  handleEventClickFilter($item) {
    const dataValueRaw = ($item.data('value') || '').toLowerCase();
    const dataParams = ($item.data('param') || '').toLowerCase();
    const dataValues = dataValueRaw.split(',');
    const isSelected = dataValues.every((val) => {
      return (
        this.convertStringParamsToArray(this.paramCardTypes).includes(val) ||
        this.convertStringParamsToArray(this.paramProducts).includes(val) ||
        this.convertStringParamsToArray(this.paramTypes).includes(val)
      );
    });

    $item.toggleClass(this.CLASS_FILTER_SELECTED, !isSelected);

    dataValues.forEach((val) => {
      this.handleUpdateParamsUrl(val, dataParams, isSelected);
    });
  }

  init() {
    this.initRenderUIFilter();
    // Initialize secondary filter instance on first load
    this.getSecondaryFilterInstance();
  }

  // Cleanup method to destroy instances and prevent memory leaks
  destroy() {
    // Use static method to destroy singleton instance
    SecondaryFilterComponent.destroyInstance();
    this.secondaryFilterInstance = null;
  }
}
